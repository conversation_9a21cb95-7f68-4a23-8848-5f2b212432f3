{"name": "zurno-api", "version": "1.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node build/bin/server.js", "queue": "node ace queue:listen --queue=default --queue=webhook --queue=syncData --queue=liveStream --queue=event-registrations --queue=tracking --queue=notification --queue=chatBot --queue=syncInventory --queue=draft-order --queue=syncProductData", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 && node ace build --ignore-ts-errors", "dev": "node ace serve --watch", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "prepare": "husky install"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#constants/*": "./app/constants/*.js", "#mails/*": "./app/mails/*.js", "#services/*": ["./app/services/*.js", "./app/services/chatbot/*/*.txt", "./app/services/chatbot/*/*.pdf"], "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js", "#guard/*": "./app/auth/guards/*.js", "#adminControllers/*": "./admin/controllers/*.js", "#jobs/*": "./app/jobs/*.js", "#audit_resolvers/*": "./app/audit_resolvers/*.js", "#credentials/*": "./app/credentials/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.7.0", "@adonisjs/eslint-config": "^1.3.0", "@adonisjs/prettier-config": "^1.3.0", "@adonisjs/tsconfig": "^1.3.0", "@eslint/js": "^9.12.0", "@japa/assert": "^3.0.0", "@japa/plugin-adonisjs": "^3.0.1", "@japa/runner": "^3.1.4", "@swc/core": "^1.6.5", "@types/luxon": "^3.4.2", "@types/node": "^20.14.9", "@types/node-cron": "^3.0.11", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "hot-hook": "^0.2.6", "nodemon": "^3.1.10", "pino-pretty": "^11.2.1", "prettier": "^3.3.2", "ts-node": "^10.9.2", "typescript": "~5.4", "typescript-eslint": "^8.8.1"}, "dependencies": {"@adonisjs/auth": "^9.2.3", "@adonisjs/bouncer": "^3.1.3", "@adonisjs/core": "^6.14.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.2.0", "@adonisjs/lucid": "^21.2.0", "@adonisjs/mail": "^9.2.2", "@adonisjs/redis": "^9.1.0", "@adonisjs/static": "^1.1.1", "@adonisjs/vite": "^3.0.0", "@aws-sdk/client-cloudwatch": "^3.750.0", "@aws-sdk/client-connect": "^3.738.0", "@aws-sdk/client-ivs": "^3.738.0", "@aws-sdk/client-ivschat": "^3.744.0", "@aws-sdk/client-polly": "^3.821.0", "@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/client-sns": "^3.738.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@google-cloud/bigquery": "^7.9.1", "@iconify-json/heroicons": "^1.2.2", "@japa/api-client": "^3.0.3", "@pinecone-database/pinecone": "latest", "@rlanz/bull-queue": "^3.0.0", "@shopify/admin-api-client": "^1.0.2", "@stouder-io/adonis-auditing": "^1.1.8", "@types/fluent-ffmpeg": "^2.1.27", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.7", "@types/ws": "^8.18.1", "@types/yamljs": "^0.2.34", "@vinejs/vine": "^2.1.0", "adonis-autoswagger": "^3.63.0", "compute-cosine-similarity": "^1.1.0", "cross-env": "^7.0.3", "crypto": "^1.0.1", "date-fns": "^4.1.0", "edge-iconify": "^2.0.1", "edge.js": "^6.0.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^5.2.1", "exceljs": "^4.4.0", "fast-csv": "^5.0.2", "ffmpeg": "^0.0.4", "firebase-admin": "^12.4.0", "fluent-ffmpeg": "^2.1.3", "gpt-tokenizer": "^2.8.1", "husky": "^9.1.6", "js-md5": "^0.8.3", "jsonwebtoken": "^9.0.2", "lint-staged": "^15.2.10", "luxon": "^3.5.0", "moment": "^2.30.1", "mysql2": "^3.11.0", "node-cron": "^3.0.3", "openai": "^4.93.0", "puppeteer": "^24.13.0", "reflect-metadata": "^0.2.2", "sharp": "^0.32.6", "socket.io": "^4.8.1", "twilio": "^5.7.0", "uuid": "^10.0.0", "vite": "^5.4.10", "yamljs": "^0.3.0", "yarn": "^1.22.22"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts", "./admin/controllers/**/*.ts"]}, "prettier": "@adonisjs/prettier-config"}