/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminSalonConstructionServiceController = () =>
  import('#adminControllers/salon_construction_service/admin_salon_construction_service_controller')

export default function adminSalonConstructionServiceRoutes() {
  router
    .group(() => {
      router.get('salon-construction-service/stats', [
        AdminSalonConstructionServiceController,
        'stats',
      ])
      router.get('salon-construction-service/:id/pdf', [
        AdminSalonConstructionServiceController,
        'downloadPdf',
      ])
      router.resource('salon-construction-service', AdminSalonConstructionServiceController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
