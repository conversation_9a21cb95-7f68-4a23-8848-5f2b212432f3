import vine from '@vinejs/vine'

export const createProductValidator = vine.compile(
  vine.object({
    id: vine.string().uuid().optional(),

    title: vine.string(),
    description: vine.string().optional(),
    status: vine.enum(['active', 'archived', 'draft']).optional(),

    vendor: vine.object({
      companyName: vine.string()
    }).optional(),

    productType: vine.object({
      name: vine.string()
    }).optional(),

    tags: vine.array(
      vine.object({
        name: vine.string()
      })
    ).optional(),

    category: vine.object({
      shopifyId: vine.string()
    }).optional(),

    images: vine.array(
      vine.object({
        src: vine.string(),
        position: vine.number(),
      })
    ).optional(),

    options: vine.array(
      vine.object({
        name: vine.string(),
        position: vine.number().optional(),
        productOptionValues: vine.array(
          vine.object({
            value: vine.string()
          })
        ).optional()
      })
    ).optional(),

    variants: vine.array(
      vine.object({
        sku: vine.string().optional(),
        barcode: vine.string().optional(),
        inventoryQuantity: vine.number().optional(),
        inventoryPolicy: vine.enum(['continue', 'deny']).optional(),

        price: vine.number().optional(),
        compareAtPrice: vine.number().optional(),

        weight: vine.number().optional(),
        weightUnit: vine.string().optional(),

        optionValues: vine.array(
          vine.object({
            value: vine.string(),
            option: vine.object({
              name: vine.string(),
            })
          })
        ),

        image: vine.object({
          src: vine.string().url(),
        }).optional(),
      })
    ),
    
    collectionIds: vine
      .array(
        vine.string().exists(async (query, field) => {
          const collection = await query.from('zn_collections').where('id', field).first()
          return !!collection
        })
      )
      .optional(),

    productTypeName: vine.string().optional(),

    vendorName: vine.string().optional(),

    tagNames: vine.array(vine.string()).optional(),
  })
)

export const createProductVendorValidator = vine.compile(
  vine.object({
    name: vine.string().unique(async (query, field) => {
      const vendor = await query.from('zn_vendors').where('companyName', field).first()
      return !vendor
    }),
  })
)

export const updateProductVendorValidator = (productVendorId: string) =>
  vine.compile(
    vine.object({
      name: vine.string().unique(async (query, field) => {
        const vendor = await query.from('zn_vendors').where('companyName', field).first()
        return vendor ? vendor.id == productVendorId : true
      }),
    })
  )

export const createProductTypeValidator = vine.compile(
  vine.object({
    name: vine.string().unique(async (query, field) => {
      const productType = await query.from('zn_product_types').where('name', field).first()
      return !productType
    }),
  })
)

export const updateProductTypeValidator = (productTypeId: string) =>
  vine.compile(
    vine.object({
      name: vine.string().unique(async (query, field) => {
        const productType = await query.from('zn_product_types').where('name', field).first()
        return productType ? productType.id == productTypeId : true
      }),
    })
  )

export const createProductTagValidator = vine.compile(
  vine.object({
    name: vine.string().unique(async (query, field) => {
      const tag = await query.from('zn_product_tags').where('name', field).first()
      return !tag
    }),
  })
)

export const updateProductTagValidator = (productTagId: string) =>
  vine.compile(
    vine.object({
      name: vine.string().unique(async (query, field) => {
        const tag = await query.from('zn_product_tags').where('name', field).first()
        return tag ? tag.id == productTagId : true
      }),
    })
  )
