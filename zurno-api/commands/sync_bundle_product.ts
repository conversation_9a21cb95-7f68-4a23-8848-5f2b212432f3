import SyncAllBundleProductJob from '#jobs/sync_all_bundle_product_job'
import ZnProduct from '#models/zn_product'
import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from '@rlanz/bull-queue/services/main'

export default class SyncBundleProduct extends BaseCommand {
  static commandName = 'sync:bundle-product'
  static description = ''

  static options: CommandOptions = { startApp: true }

  async run() {
    const batchSize = 20;
    let offset = 0;
    let hasMore = true;
    const bundleTagList = (process.env.PRODUCT_BUNDLE_TAG_NAME || '')
      .split(',')

    while (hasMore) {
      const products = await ZnProduct.query()
        .whereHas('tags', (tagQuery) => {
          tagQuery.whereIn('name', bundleTagList)
        })
        .limit(batchSize).offset(offset);

      if (products.length === 0) {
        hasMore = false;
        break;
      }

      await queue.dispatch(
        SyncAllBundleProductJob,
        { products },
        {
          queueName: 'syncProductData',
        }
      );

      offset += batchSize;
    }
  }
}
