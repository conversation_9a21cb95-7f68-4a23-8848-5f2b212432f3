import { PostService } from '#services/post_service';
import { args, BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'transcode:video'
  static description = 'Transcode post video to h264'

  static options: CommandOptions = {
    startApp: true,
  }

  @args.string({
    required: true,
    description: "Id of Post"
  })
  declare postId: string

  async run() {
    const postService = new PostService()

    try {      
      await postService.transcodeVideoSync({ postId: this.postId })

    } catch (error) {
      this.logger.error(error);
    }
  }
}
