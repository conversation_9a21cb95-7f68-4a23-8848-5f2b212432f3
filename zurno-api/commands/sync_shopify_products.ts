import StoreShopifyProductsJob from '#jobs/store_shopify_products_job'
import SyncProductPriceJob from '#jobs/sync_product_price_job'
import { ShopifyService } from '#services/shopify/shopify_service'
import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from '@rlanz/bull-queue/services/main'

export default class SyncShopifyProducts extends BaseCommand {
  static commandName = 'shopify:sync-products'
  static description = 'Sync all products from shopify'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const shopifyService = new ShopifyService()
    let endCursor = null
    let hasNextPage = true

    while (hasNextPage) {
      const { products, pageInfo } = await shopifyService.fetchProductsFromShopify(endCursor)
      await queue.dispatch(
        StoreShopifyProductsJob,
        { products },
        {
          queueName: 'syncProductData',
        }
      )

      // Pagination
      hasNextPage = pageInfo.hasNextPage
      endCursor = pageInfo.endCursor
    }

    await queue.dispatch(
      SyncProductPriceJob,
      {},
      {
        queueName: 'syncProductData',
      }
    )
  }
}
