import { middleware } from "#start/kernel";
import router from "@adonisjs/core/services/router";

const VendorController = () => import("#controllers/vendor_controller");
const VendorEarningController = () => import("#controllers/vendor_earning_controller");
const VendorPaymentController = () => import("#controllers/vendor_payment_controller");
const VendorPaymentMethodController = () => import("#controllers/vendor_payment_method_controller");
const VendorOrdersController = () => import("#controllers/app/vendor/vendor_orders_controller");

export default function vendorRoutes() {
  router
    .group(() => {
      router.post('/', [VendorController, 'store']);
    })
    .prefix('vendors');


  router
    .group(() => {
      router.get('/earnings', [VendorEarningController, 'index']);
      router.get('/earnings/:id', [VendorEarningController, 'show']);

      router.get('/payments', [VendorPaymentController, 'index']);

      router.get('/', [VendorController, 'show']);
      router.get('/stats', [VendorController, 'stats']);
      router.put('/', [VendorController, 'update']);

      router.get('/orders', [VendorOrdersController, 'index']);
      router.get('/orders/:id', [VendorOrdersController, 'show']);
      router.put('/orders/:id', [VendorOrdersController, 'update']);

      router.get('/top-selling-products', [VendorController, 'topSellingProducts']);
      router.get('/sale-stats', [VendorController, 'getSalesStatistics']);
      router.get('/sale-values', [VendorController, 'getSalesValues']);
      router.get('/monthly-profits', [VendorController, 'getVendorMonthlyProfitSummary']);
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    .prefix('vendors');

  router
    .group(() => {
      router.patch('/vendors/payment-methods/:id/set-default', [VendorPaymentMethodController, 'setDefaultMethod'])
      router.resource('/vendors/payment-methods', VendorPaymentMethodController);
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }));
}
