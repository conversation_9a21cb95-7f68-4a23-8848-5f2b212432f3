import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_vendors'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('commissionRate', 15, 6).defaultTo(0).alter()
      table.decimal('fixedCommissionAmount', 15, 6).defaultTo(0).alter()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.float('fixedCommissionAmount', 8, 2).defaultTo(0).alter()
      table.float('commissionRate', 8, 2).defaultTo(0).alter()
    })
  }
}