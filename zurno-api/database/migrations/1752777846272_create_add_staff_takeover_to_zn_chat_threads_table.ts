import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_chat_threads'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('isStaffResponding')
        .notNullable()
        .defaultTo(false)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table)=> {
      table.dropColumn('isStaffResponding')
    })
  }
}
