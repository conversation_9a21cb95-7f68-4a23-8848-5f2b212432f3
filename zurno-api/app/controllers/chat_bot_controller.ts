import ChatBotMessageJob from '#jobs/chat_bot_message_job'
import Chat<PERSON><PERSON><PERSON><PERSON>ob from '#jobs/chat_message_job'
import ZnAIAssistant, { EAIAssistantRole } from '#models/zn_ai_assistant'
import ZnChatMessage from '#models/zn_chat_message'
import Zn<PERSON>hatRoom from '#models/zn_chat_room'
import ZnChatThread from '#models/zn_chat_thread'
import ZnCollection from '#models/zn_collection'
import ZnPost from '#models/zn_post'
import ZnProduct from '#models/zn_product'
import ZnUser from '#models/zn_user'
import { IvschatService } from '#services/aws/ivschat_service'
import { HttpContext } from '@adonisjs/core/http'
import queue from '@rlanz/bull-queue/services/main'
import OpenAI from 'openai'
import ThreadsDeleteJob from "#jobs/threads_delete_job";
import {TRANSLATIONS_ENUM} from "#constants/app";
// import {IvsChatBotService} from "#services/aws/ivschat_bot_service";



export default class ChatBotController {
    private ivsChatService: IvschatService
    private openai: OpenAI

  constructor() {
    this.ivsChatService = new IvschatService()
    this.openai = new OpenAI()
  }

    /**
     * @createChatToken
     * @tag Chat Bot
     * @summary Chat Token provider
     * @paramPath id - ID of Chat Room - @required
     * @responseBody 200 - {"token":"","tokenExpirationTime":"2025-02-18T22:44:57.000Z","tokenExpirationTime","2025-02-18T22:44:57.000Z"} - ChatToken provider for IVS ChatRoom
     */
    async createChatToken({ auth, params, response }: HttpContext) {
        try {
            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound({ message: 'Chat Room Not Found' }) }

            const requestUser = auth.getUserOrFail() as ZnUser
            // const requestUser = await ZnUser.findOrFail('1bfd1695-5266-46c5-8c8f-aa325fc0f465')
            const user = await ZnUser.find(requestUser.id)
            if (!user) { return response.notFound({ message: 'User Not Found' }) }

            const attributes = {
                type: 'user',
                email: user?.email,
                firstName: user?.firstName,
                lastName: user?.lastName,
                fullname: user?.fullname,
                avatarUrl: user?.avatarUrl,
                avatarMediaUrl: user?.avatarMedia?.url
            }

            const token = await this.ivsChatService.createChatToken({
                roomArn: room.arn,
                userId: user.id,
                capabilities: ['SEND_MESSAGE'],
                attributes
            })

            return response.ok(token)

        } catch (error) {
            console.log(error);
            return response.internalServerError(error)
        }
    }

    // /**
    //  * @createChatRoom
    //  * @tag Chat Bot
    //  * @summary Create Chat Bot Room
    //  * @requestBody {}
    //  * @responseBody 200 - <ZnChatRoom> - Create Chat Bot Room
    //  */
    // async createChatRoom({ request, response }: HttpContext) {
    //     try {
    //         const room = await this.ivsChatService.createRoom({ name: 'chat-bot-room' })

    //         const dbRoom = await ZnChatRoom.create({
    //             arn: room.arn,
    //             name: 'chat-bot-room',
    //         })

    //         const thread = await this.openai.beta.threads.create()

    //         const requestUserId = this.getRequestUserId(request)

    //         await ZnChatThread.create({
    //             roomId: dbRoom.id,
    //             userId: requestUserId,
    //             openaiThreadId: thread.id,
    //         })

    //         return response.created(dbRoom)

    //     } catch (error) {
    //         console.log(error);
    //         return response.internalServerError(error)
    //     }
    // }

    /**
     * @getChatRoom
     * @tag Chat Bot
     * @summary Get Chat Bot Room
     * @responseBody 200 - <ZnChatRoom>.with(messages) - Get Chat Bot Room descriptively
     */
    async getChatRoom({ auth, response }: HttpContext) {
        // const {
        //     roomId,
        //     userId
        // } = request.qs()

        const requestUser = auth.getUserOrFail() as ZnUser
        // const requestUser = await ZnUser.findOrFail('1bfd1695-5266-46c5-8c8f-aa325fc0f465')

        try {
            const thread = await ZnChatThread.query()
                .where({ userId: requestUser.id })
                .preload('room', (roomQuery) => {
                    roomQuery
                        .preload('messages', (messageQuery) => {
                            messageQuery
                                .preload('user', (userQuery) => {
                                    userQuery.preload('avatarMedia')
                                })
                                .orderBy('createdAt', 'desc')
                        })
                })
                .first()

            let room: ZnChatRoom | null = null
            if (!thread) {
                const ivsRoom = await this.ivsChatService.createRoom({ name: 'chat-bot-room' })

                const dbRoom = await ZnChatRoom.create({
                    arn: ivsRoom.arn,
                    name: 'chat-bot-room',
                })

                const thread = await this.openai.beta.threads.create()

                await ZnChatThread.create({
                    roomId: dbRoom.id,
                    userId: requestUser.id,
                    openaiThreadId: thread.id,
                })

                await dbRoom.load('messages')

                room = dbRoom
            } else {
                room = thread.room
            }
            //Show introduction message if message is empty
            if (room?.messages.length == 0) {

              const message = await ZnChatMessage.create({
                roomId: room.id,
                content: `How can I help you, ${requestUser.firstName}?`
              })

              room.messages.push(message)
            }
            return response.ok(room)
        } catch (error) {
            console.log(error);
            return response.internalServerError(error)
        }
    }

    /**
     * @createChatMessage
     * @tag Chat Bot
     * @summary Create chat message with bot
     * @description Send job to save chat message as post comment<br><br>Send out response directly to via room's websocket with attributes { productIds:"[]", collectionIds:"[]", postIds:"[]", questions:"[]" }
     * @paramPath id - ID of Chat Room - @type(string)  @required
     * @requestBody {"id":"wNbmMLv3EuWl","content":"A message"}
     * @responseBody 200 - Sent - OK
     */
    async createChatMessage({ auth, params, request, response }: HttpContext) {
        try {
            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)

            const thread = await ZnChatThread.findBy({ roomId })
            if (!room || !thread) { return response.notFound("Chat Room Not Found") }

            const isStaffResponding = thread.isStaffResponding

            const user = auth.getUserOrFail() as ZnUser
            if (thread.userId != user.id) {
                return response.unauthorized("Cannot post chat to another person's room")
            }

            const assistant = await ZnAIAssistant.findBy({ role: EAIAssistantRole.CUSTOMER_SERVICE })
            if (!assistant) { return response.badRequest("No AI Assistant to response") }

            const data = request.body() as any

            // save the message
            await queue.dispatch(
                ChatMessageJob,
                {
                    roomId,
                    data: {
                        ...data,
                        sender: {
                            userId: user.id,
                        }
                    },
                },
                { attempts: 1, queueName: "chatBot" }
            )

            // start the bot replying process
            if (!isStaffResponding) {
              await queue.dispatch(
                ChatBotMessageJob,
                {
                  roomId,
                  data,
                  userId: user.id
                },
                {attempts: 1, queueName: "chatBot"},
              )
              return response.ok('Sent')
            }

        } catch (error) {
            console.log(error);
            return response.internalServerError(error)
        }
    }

    /**
     * @getChatMessages
     * @tag Chat Bot
     * @summary Get chat messages with bot
     * @paramPath id - ID of Chat Room - @type(string) @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 1) - @type(number)
     * @paramQuery search - Search Term - @type(string)
     * @responseBody 200 - <ZnChatMessage[]>.paginated() - Get chat messages with bot, newest to oldest
     */
    async getChatMessages({ params, request, response }: HttpContext) {
        try {
            const {
                search = '',
                page = 1,
                limit = 10,
            } = request.qs()

            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound("Chat Room Not Found") }

            const query = ZnChatMessage.query()
                .where({ roomId })
                .preload('user', (userQuery) => {
                    userQuery.preload('avatarMedia')
                })
                .orderBy('createdAt', 'desc')

            if (search) {
                const searchTerms = `%${search.split(' ').filter(Boolean).join('%')}%`
                query.andWhereILike('content', searchTerms)
            }

            const result = await query.paginate(page, limit)

            return response.ok(result)

        } catch (error) {
            console.log(error);
            return response.internalServerError(error)
        }
    }

    /**
     * @clearChatMessages
     * @tag Chat Bot
     * @summary Clear chat messages
     * @paramPath id - ID of Chat Room - @required
     * @responseBody 200 - Chat messages cleared - Clear chat messages descriptively
     */
    async clearChatMessages({ auth, params, response }: HttpContext) {
        try {
            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)

            const thread = await ZnChatThread.findBy({ roomId })
            await queue.dispatch(
              ThreadsDeleteJob,
              {threadIds: thread ? [thread.id] : []},
              { attempts: 1, queueName: "chatBot" }
            )
            if (!room || !thread) { return response.notFound("Chat Room Not Found") }

            const user = auth.getUserOrFail() as ZnUser
            if (thread.userId != user.id) {
                return response.unauthorized("Cannot clear messages from another person's room")
            }

            const messages = await ZnChatMessage.query()
                .where({ roomId })

            for (const message of messages) {
                await message.softDelete()
            }

            return response.ok("Chat messages cleared")

        } catch (error) {
            console.log(error);
            return response.internalServerError(error)
        }
    }

    /**
     * @getChatSuggestions
     * @tag Chat Bot
     * @summary Get three random quick-reply suggestions in the user’s locale
     * @responseBody 200 - ["Where is my order?", "…", "…"] - Array of 3 strings
     * @security bearerAuth   // <-- if you document auth that way
     */
    async getChatSuggestions({ auth, response }: HttpContext) {
      const CHAT_SUGGESTIONS: Record<TRANSLATIONS_ENUM, string[]> = {
        [TRANSLATIONS_ENUM.EN]: [
          'Where is my order?',
          'Track my package status.',
          'Show me the newest products.',
          "What's on sale right now?",
          'Recommend a gel polish color.',
          'What are your best sellers?',
          'Connect me with customer support.',
        ],

        [TRANSLATIONS_ENUM.ES]: [
          '¿Dónde está mi pedido?',
          'Rastrear el estado de mi paquete.',
          'Muéstrame los productos más nuevos.',
          '¿Qué está en oferta ahora?',
          'Recomiéndame un color de esmalte en gel.',
          '¿Cuáles son sus más vendidos?',
          'Conéctame con atención al cliente.',
        ],

        [TRANSLATIONS_ENUM.VI]: [
          'Đơn hàng của tôi đang ở đâu?',
          'Theo dõi trạng thái gói hàng của tôi.',
          'Cho tôi xem sản phẩm mới nhất.',
          'Hiện đang giảm giá những gì?',
          'Gợi ý cho tôi màu sơn gel.',
          'Sản phẩm bán chạy nhất là gì?',
          'Kết nối tôi với bộ phận hỗ trợ khách hàng.',
        ],
      }
      const user = auth.getUserOrFail() as ZnUser
      let language = user?.locale ?? TRANSLATIONS_ENUM.EN
      let questions = CHAT_SUGGESTIONS[language]
      questions = questions.sort( () => Math.random() -0.5).slice(0,3)
      return response.ok(questions)
    }

    /**
     * @listChatResources
     * @tag Chat Bot
     * @summary List Chat Resources
     * @requestBody {"productIds":"['']","collectionIds":"['']","postIds":"['']"}
     * @responseBody 200 - {"products":["ZnProduct"],"collections":["ZnCollection"],"posts":["ZnPost"]} - Get chat suggestions descriptively
     */
    async listChatResources({ request, response }: HttpContext) {
        try {
            const {
                productIds,
                collectionIds,
                postIds,
            } = request.body()

            const productIdArray = this.getParsedArray(productIds)
            const collectionIdArray = this.getParsedArray(collectionIds)
            const postIdArray = this.getParsedArray(postIds)

            let products
            if (productIdArray) {
                products = await ZnProduct.query()
                    .whereIn('id', productIdArray)
                    .whereNot('status', 'draft')
                    .where('isGift', false)
                    .preload('variant')
                    .preload('image')
                    .preload('reviewsSummary')
            }

            let collections
            if (collectionIdArray) {
                collections = await ZnCollection.query()
                    .whereIn('id', collectionIdArray)
                    .where('status', true)
            }

            let posts
            if (postIdArray) {
                posts = await ZnPost.query()
                    .whereIn('id', postIdArray)
                    .where({
                        expired: false,
                        isDraft: false,
                        isUnlist: false
                    })
                    .preload('thumbnail')
                    .preload('medias')
            }


            return {
                products,
                collections,
                posts,
            }


        } catch (error) {
            console.log(error);
            return response.internalServerError({
                message: "Something went wrong!",
                error
            })
        }
    }

    /**
     * Get Array from string or normal array
     */
    private getParsedArray(input?: string | string[]) {
        if (!input) { return }

        let outputArray = []
        if (Array.isArray(input)) {
            outputArray = input
        } else {
            try {
                outputArray = JSON.parse(input)
            } catch {
                let leftBracketIdx = input.indexOf('[')
                let rightBracketIdx = input.indexOf(']')
                if (rightBracketIdx < 0) { rightBracketIdx = input.length }

                outputArray = input
                    .slice(leftBracketIdx + 1, rightBracketIdx)
                    .replaceAll('\'', '')
                    .split(',')
            }
        }

        return outputArray
    }

}
