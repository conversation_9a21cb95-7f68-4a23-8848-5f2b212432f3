import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";
import { vendorCreateValidator, vendorUpdateValidator } from "../validators/vendor_validator.js";

export default class VendorController {
  private vendorService: VendorService;

  constructor() {
    this.vendorService = new VendorService();
  }

  async store({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(vendorCreateValidator);
      const result = await this.vendorService.createVendor(payload);
      if (result.success)
        return response.created(result.vendor);
      else
        return response.conflict(result.messagge);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async show({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);

      return response.ok(vendor);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async stats({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const stats = await this.vendorService.getVendorStats(vendor.id);

      return response.ok(stats);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async update({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      if (!user.vendorId) throw new Error("User doesn't belong to any vendor");

      const payload = await request.validateUsing(vendorUpdateValidator);

      return await this.vendorService.update(user.vendorId, payload);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async topSellingProducts({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const data = await this.vendorService.getTopSellingProducts(vendor.id);

      return response.ok(data);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async getSalesStatistics({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const data = await this.vendorService.getSalesStatistics(vendor.id);

      return response.ok(data);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async getSalesValues({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const data = await this.vendorService.getMonthlySalesSummary(vendor.id);

      return response.ok(data);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async getVendorMonthlyProfitSummary({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const data = await this.vendorService.getMonthlyProfitSummary(vendor.id);

      return response.ok(data);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }
}
