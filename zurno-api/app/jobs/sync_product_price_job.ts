import ZnProduct from '#models/zn_product'
import { Job } from '@rlanz/bull-queue'

interface SyncProductPriceJobPayload {}

export default class SyncProductPriceJob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({}: SyncProductPriceJobPayload) {
    const products = await ZnProduct.query()
      .whereNull('price')
      .preload('variants')
    for (const product of products) {
      const variant = await product.related('variants').query().first()
      if (variant) {
        await product
          .merge({
            price: variant.price,
            compareAtPrice: variant?.compareAtPrice,
          })
          .save()
      }
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SyncProductPriceJobPayload) {}
}
