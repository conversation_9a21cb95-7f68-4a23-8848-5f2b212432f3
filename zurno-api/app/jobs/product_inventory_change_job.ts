import { ProductService } from '#services/shop/product_service'
import { Job } from '@rlanz/bull-queue'

interface ProductInventoryChangeJobPayload {
  productId: string,
  shopifyLocationId: string
}

export default class ProductInventoryChangeJob extends Job {
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ productId, shopifyLocationId }: ProductInventoryChangeJobPayload) {
    const productService = new ProductService()

    await productService.changeInventory(productId, shopifyLocationId)
  }

  async rescue() { }
}
