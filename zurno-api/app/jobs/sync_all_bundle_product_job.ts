import ZnProduct from '#models/zn_product'
import { SyncBundleProductService } from '#services/sync/sync_bundle_product_service'
import { Job } from '@rlanz/bull-queue'
import {getIdFromShopifyId} from "../../services/commons.js";
import {FastBundleService} from "../services/shopify/fast_bundle_service.js";

interface SyncAllBundleProductJobPayload {
  products: ZnProduct[]
}

export default class SyncAllBundleProductJob extends Job {
  // This is the path to the file that is used to create the job
  private fastBundleService = new FastBundleService()
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ products }: SyncAllBundleProductJobPayload) {
    const syncBundleProductService = new SyncBundleProductService()
    for (const product of products) {
      const shopifyId = getIdFromShopifyId(product.shopifyProductId)
      const bundle = await this.fastBundleService.getBundleByBundleProductId(shopifyId)
      if (bundle) {
        await syncBundleProductService.syncBundleProductByBundle([bundle])
      }
      // await syncBundleProductService.syncBundleProduct(product)
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SyncAllBundleProductJobPayload) {}
}
