import {Job} from "@rlanz/bull-queue";
import ZnChatMessage from "#models/zn_chat_message";

interface AnnotateChatMessageJobPayload {
  roomId: string
  data : {
    annotatedChatMessageId: string
    content: string
    adminId: string
    sendTime?: string,
  }
}

export default class AnnotatedChatMessageJob extends Job {
  static get $$filepath() {
    return import.meta.url
  }
  async handle( {roomId, data} : AnnotateChatMessageJobPayload) {
    await ZnChatMessage.create({
      roomId: roomId,
      adminId: data.adminId,
      content: data.content,
      annotatedChatMessageId: data.annotatedChatMessageId,
    })
  }

  async rescue() {}


}
