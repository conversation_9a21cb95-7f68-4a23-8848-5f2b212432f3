import AppModel from "#models/app_model";
import { belongsTo, column } from "@adonisjs/lucid/orm";
import type { BelongsTo } from "@adonisjs/lucid/types/relations";
import ZnChatRoom from "./zn_chat_room.js";
import ZnUser from "./zn_user.js";
import ZnAdmin from "#models/zn_admin";

export default class ZnChatThread extends AppModel {
  @column({ columnName: 'roomId' })
  declare roomId: string

  @belongsTo(() => ZnChatRoom, {
    foreignKey: 'roomId'
  })
  declare room: BelongsTo<typeof ZnChatRoom>

  @column({ columnName: 'userId' })
  declare userId: string | null

  @belongsTo(() => ZnUser, {
    foreignKey: 'userId'
  })
  declare user: BelongsTo<typeof ZnUser>

  @column({ columnName: 'adminId' })
  declare adminId: string | null

  @belongsTo(() => ZnAdmin, {
    foreignKey: 'adminId',
  })
  declare admin: BelongsTo<typeof ZnAdmin>

  @column({ columnName: 'openaiThreadId' })
  declare openaiThreadId: string

  @column({ columnName: 'isStaffResponding'})
  declare isStaffResponding: boolean

}


