import { column, computed } from '@adonisjs/lucid/orm'
import AppModel from './app_model.js'

export default class ZnAddress extends AppModel {
  static table = 'zn_addresses'

  @column({ columnName: 'userId' })
  declare userId: string

  @column({ columnName: 'shopifyId' })
  declare shopifyId?: string | null

  @column({ columnName: 'firstName' })
  declare firstName?: string | null

  @column({ columnName: 'lastName' })
  declare lastName?: string | null

  @column({ columnName: 'company' })
  declare company?: string | null

  @column({ columnName: 'name' })
  declare name: string

  @column({ columnName: 'address1' })
  declare address1: string

  @column({ columnName: 'address2' })
  declare address2?: string | null

  @column({ columnName: 'city' })
  declare city: string

  @column({ columnName: 'province' })
  declare province?: string | null

  @column({ columnName: 'country' })
  declare country: string

  @column({ columnName: 'zip' })
  declare zip?: string | null

  @column({ columnName: 'phone' })
  declare phone?: string | null

  @column({ columnName: 'provinceCode' })
  declare provinceCode?: string | null

  @column({ columnName: 'countryCode' })
  declare countryCode?: string | null

  @column({ columnName: 'latitude' })
  declare latitude?: string | null

  @column({ columnName: 'longitude' })
  declare longitude?: string | null

  @computed()
  get formatAddress() {
    return `${this.address1}${this.address2 ?? ''}, ${this.city}, ${this.province ?? ''} ${this.zip ?? ''}, ${this.country}`;
  }
}
