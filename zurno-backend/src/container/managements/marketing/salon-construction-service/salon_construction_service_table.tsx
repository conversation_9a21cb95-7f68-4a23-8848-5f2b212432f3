import React, { useState, useEffect } from "react";
import { Card, Row, Col, Form, Button, Badge, Dropdown } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { toast } from "react-hot-toast";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import {
  useLazyListSalonConstructionServiceQuery,
  useLazyGetSalonConstructionServiceStatsQuery,
  useUpdateSalonConstructionServiceStatusMutation,
  salonConstructionServiceHelpers,
} from "../../../../services/marketing/salon-construction-service";
import {
  ESalonConstructionServiceStatus,
  STATUS_LABELS,
  STATUS_COLORS,
  BUDGET_RANGE_LABELS,
  SERVICE_INTEREST_LABELS,
} from "../../../../utils/constant/salon-construction-service";

const SalonConstructionServiceTable: React.FC = () => {
  const [filters, setFilters] = useState<TSalonConstructionServiceFilters>({
    status: "all",
    search: "",
    startDate: "",
    endDate: "",
    budgetRange: "all",
    page: 1,
    limit: 10,
  });

  const [triggerListSignups, { data: signupsData, isLoading: signupsLoading }] =
    useLazyListSalonConstructionServiceQuery();
  const [triggerGetStats, { data: statistics }] =
    useLazyGetSalonConstructionServiceStatsQuery();
  const [updateStatus] = useUpdateSalonConstructionServiceStatusMutation();

  const fetchSignups = async () => {
    try {
      let filterArray: string[] = [];

      if (filters.status !== "all") {
        filterArray.push(`status||$eq||${filters.status}`);
      }

      if (filters.startDate) {
        filterArray.push(`createdAt||$gte||${filters.startDate}`);
      }

      if (filters.endDate) {
        filterArray.push(`createdAt||$lte||${filters.endDate}`);
      }

      if (filters.budgetRange !== "all") {
        filterArray.push(`budgetRange||$eq||${filters.budgetRange}`);
      }

      const params = {
        page: filters.page,
        limit: filters.limit,
        ...(filterArray.length > 0 && { filter: filterArray }),
        ...(filters.search && {
          s: `{$or:[{fullName:{$contL:"${filters.search}"}},{businessName:{$contL:"${filters.search}"}},{emailAddress:{$contL:"${filters.search}"}}]}`,
        }),
      };

      await triggerListSignups(params);
    } catch (error) {
      console.error("Error fetching signups:", error);
      toast.error("Failed to fetch salon construction service signups");
    }
  };

  const fetchStatistics = async () => {
    try {
      await triggerGetStats();
    } catch (error) {
      console.error("Error fetching statistics:", error);
    }
  };

  useEffect(() => {
    fetchSignups();
  }, [filters]);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const handleFilterChange = (
    key: keyof TSalonConstructionServiceFilters,
    value: any
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: key !== "page" ? 1 : value,
    }));
  };

  const handlePageChange = (page: number) => {
    handleFilterChange("page", page);
  };

  const handleStatusUpdate = async (
    id: string,
    status: ESalonConstructionServiceStatus,
    rejectionReason?: string
  ) => {
    try {
      if (status === ESalonConstructionServiceStatus.APPROVED) {
        await updateStatus(salonConstructionServiceHelpers.approveSignup(id));
        toast.success("Signup approved successfully");
      } else if (status === ESalonConstructionServiceStatus.REJECTED) {
        await updateStatus(
          salonConstructionServiceHelpers.rejectSignup(id, rejectionReason)
        );
        toast.success("Signup rejected successfully");
      }

      fetchSignups();
      fetchStatistics();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update signup status");
    }
  };

  const handleDownloadPDF = (id: string) => {
    try {
      salonConstructionServiceHelpers.downloadPDF(id);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    }
  };

  // Update the status badge to be pill-shaped and visually match the screenshot
  const renderStatusBadge = (status: ESalonConstructionServiceStatus) => {
    const colorMap = {
      PENDING: "#ffb300", // yellow
      APPROVED: "#43d39e", // green
      REJECTED: "#ff5c75", // red
    };
    return (
      <span
        style={{
          background: colorMap[status] || "#e0e0e0",
          color: "#fff",
          borderRadius: 999,
          padding: "4px 16px",
          fontWeight: 600,
          fontSize: 14,
          display: "inline-block",
          minWidth: 80,
          textAlign: "center",
        }}
      >
        {STATUS_LABELS[status]}
      </span>
    );
  };

  const renderServiceInterests = (interests: string[]) => {
    return interests
      .map(
        (interest) =>
          SERVICE_INTEREST_LABELS[
            interest as keyof typeof SERVICE_INTEREST_LABELS
          ]
      )
      .join(", ");
  };

  // Update the Actions dropdown to use a purple outline style
  const renderActions = (signup: TSalonConstructionServiceSignup) => {
    return (
      <Dropdown>
        <Dropdown.Toggle
          style={{
            borderColor: "#6a82fb",
            color: "#6a82fb",
            borderRadius: 8,
            fontWeight: 600,
          }}
          size="sm"
          variant="outline-primary"
        >
          Actions
        </Dropdown.Toggle>
        <Dropdown.Menu>
          <Dropdown.Item
            as={Link}
            to={`${
              import.meta.env.BASE_URL
            }managements-salon-construction-service/details/${signup.id}`}
          >
            View Details
          </Dropdown.Item>
          {signup.pdfFile && (
            <Dropdown.Item onClick={() => handleDownloadPDF(signup.id)}>
              Download PDF
            </Dropdown.Item>
          )}
          {signup.status === ESalonConstructionServiceStatus.PENDING && (
            <>
              <Dropdown.Divider />
              <Dropdown.Item
                onClick={() =>
                  handleStatusUpdate(
                    signup.id,
                    ESalonConstructionServiceStatus.APPROVED
                  )
                }
                className="text-success"
              >
                Approve
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => {
                  const reason = prompt("Enter rejection reason (optional):");
                  handleStatusUpdate(
                    signup.id,
                    ESalonConstructionServiceStatus.REJECTED,
                    reason || undefined
                  );
                }}
                className="text-danger"
              >
                Reject
              </Dropdown.Item>
            </>
          )}
        </Dropdown.Menu>
      </Dropdown>
    );
  };

  return (
    <>
      {signupsLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Salon Construction Service"
                route="/managements-salon-construction-service"
              />
              <div className="px-4 justify-content-end">
                <Button
                  variant="primary-light"
                  onClick={() => {
                    // You can add a create button if needed, or remove this div if not required
                  }}
                  style={{ display: "none" }}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Form.Group
                  className="text-center mb-3"
                  style={{ marginRight: "1rem" }}
                >
                  <div
                    className="d-flex justify-content-center align-items-center mb-3"
                    style={{ gap: 8 }}
                  >
                    <Form.Control
                      key="search"
                      type="text"
                      name="Search"
                      value={filters.search}
                      onChange={(e) =>
                        handleFilterChange("search", e.target.value)
                      }
                      placeholder="Type a keyword..."
                      style={{ maxWidth: 300 }}
                    />
                    <Button
                      variant="light"
                      className="btn btn-light btn-sm"
                      onClick={() => handleFilterChange("search", "")}
                    >
                      X
                    </Button>
                  </div>
                </Form.Group>
                <div className="table-responsive">
                  <table className="table table-bordered text-nowrap border-bottom">
                    <thead>
                      <tr>
                        <th>Business Name</th>
                        <th>Owner Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Service Interest</th>
                        <th>Budget Range</th>
                        <th>Status</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {signupsData?.data && signupsData.data.length > 0 ? (
                        signupsData.data.map((signup) => (
                          <tr key={signup.id}>
                            <td>{signup.businessName}</td>
                            <td>{signup.fullName}</td>
                            <td>{signup.emailAddress}</td>
                            <td>{signup.phoneNumber}</td>
                            <td
                              className="text-truncate"
                              style={{ maxWidth: "200px" }}
                            >
                              {renderServiceInterests(signup.serviceInterest)}
                            </td>
                            <td>{BUDGET_RANGE_LABELS[signup.budgetRange]}</td>
                            <td>{renderStatusBadge(signup.status)}</td>
                            <td>
                              {new Date(signup.createdAt).toLocaleDateString()}
                            </td>
                            <td>{renderActions(signup)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={9} className="text-center">
                            No signups found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                <div className="d-flex justify-content-center my-3">
                  <PaginationBar
                    page={filters.page}
                    setPage={handlePageChange}
                    lastPage={Math.ceil(
                      (signupsData?.total || 0) / filters.limit
                    )}
                    limit={filters.limit}
                    total={signupsData?.total || 0}
                  />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default SalonConstructionServiceTable;
