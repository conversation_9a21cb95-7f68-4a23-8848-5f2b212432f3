import { FC, Fragment, useCallback, useEffect, useState } from "react";
import {
  Button,
  Card,
  Col,
  Form,
  InputGroup,
  OverlayTrigger,
  Pagination,
  Row,
  Table,
  Tooltip,
} from "react-bootstrap";
import { useNavigate } from "react-router-dom";

import { format } from "date-fns";
import debounce from "lodash/debounce";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import {
  useLazyListSalonConstructionServiceQuery,
  useLazyDeleteSalonConstructionServiceByIdQuery,
} from "../../../../services/marketing/salon-construction-service";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import {
  BUDGET_RANGE_LABELS,
  SERVICE_INTEREST_LABELS,
} from "../../../../utils/constant/salon-construction-service";

interface ManagementSalonConstructionServiceProps {}

const ManagementSalonConstructionService: FC<
  ManagementSalonConstructionServiceProps
> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(20);

  const [search, setSearch] = useState<string>("");

  const [isInitial, setIsInitial] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>();

  const [trigger] = useLazyListSalonConstructionServiceQuery();
  const [deleteById] = useLazyDeleteSalonConstructionServiceByIdQuery();

  const [signups, setSignups] = useState<
    TReponsePaging<TSalonConstructionServiceSignup>
  >([]);

  const navigate = useNavigate();

  const loadData = (page: number, search?: string, owners = [""]) => {
    setIsLoading(true);
    trigger({
      page,
      limit,
      search,
      ownerIds: owners,
    })
      .then((res) => {
        setTotalPages(res?.data?.meta?.lastPage);
        setSignups(res.data?.data || []);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setIsInitial(false);
        setIsLoading(false);
      });
  };

  useEffect(() => {
    loadData(page, search);
  }, [page, limit]);

  const debouncedHandleInputSearchFilter = useCallback(
    debounce((value) => {
      loadData(1, value?.search);
    }, 500),
    []
  );

  useEffect(() => {
    const query = {
      search: search,
    };
    if (!isInitial) {
      debouncedHandleInputSearchFilter(query);
    }
  }, [search]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleDeleteClick = (signupId: string) => {
    deleteSweetAlert({
      id: signupId,
      deleteAction: deleteById,
      prepareAction: () => setIsLoading(true),
      finishAction: () => loadData(page, search),
      finalAction: () => setIsLoading(false),
    });
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Salon Construction Service Management"
                route=""
              ></CardHeaderWithBack>
              <div className="px-4 justify-content-end">
                <Button
                  hidden={
                    !hasPermission(
                      ACTION.CREATE,
                      RESOURCE.SALON_CONSTRUCTION_SERVICE
                    )
                  }
                  variant="primary-light"
                  onClick={() => {
                    navigate("details");
                  }}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Form.Group
                  className="text-center mb-3"
                  style={{ marginRight: "1rem" }}
                >
                  <InputGroup className="mb-3">
                    <Form.Control
                      key="search"
                      type="text"
                      name="Search"
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      placeholder="Type a keyword..."
                    />
                    <Button
                      variant="light"
                      className="btn btn-light btn-sm"
                      onClick={() => setSearch("")}
                    >
                      X
                    </Button>
                  </InputGroup>
                </Form.Group>
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Business Name</th>
                      <th>Owner Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Service Interest</th>
                      <th>Budget Range</th>
                      <th>Status</th>
                      <th>Created Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {signups.map((signup: TSalonConstructionServiceSignup) => (
                      <Fragment key={signup.id}>
                        <ReadOnlyRow
                          signup={signup}
                          err={err}
                          setErr={setErr}
                          setIsLoading={setIsLoading}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <div className="d-flex justify-content-center my-3">
                  {" "}
                  <Pagination>
                    <Pagination.First onClick={() => handlePageChange(1)} />
                    <Pagination.Prev
                      onClick={() => handlePageChange(Math.max(page - 1, 1))}
                    />

                    {[...Array(totalPages).keys()]
                      .slice(
                        Math.max(page - 2, 0),
                        Math.min(page + 3, totalPages)
                      )
                      .map((p) => (
                        <Pagination.Item
                          key={p + 1}
                          active={p + 1 === page}
                          onClick={() => handlePageChange(p + 1)}
                        >
                          {p + 1}
                        </Pagination.Item>
                      ))}

                    <Pagination.Next
                      onClick={() =>
                        handlePageChange(Math.min(page + 1, totalPages))
                      }
                    />
                    <Pagination.Last
                      onClick={() => handlePageChange(totalPages)}
                    />
                  </Pagination>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({ signup, handleDeleteClick }: any) => {
  const navigate = useNavigate();

  return (
    <tr>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {signup.businessName}
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "120px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {signup.fullName}
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {signup.emailAddress}
      </td>
      <td>{signup.phoneNumber}</td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "200px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {signup.serviceInterest
          .map(
            (interest: string) =>
              SERVICE_INTEREST_LABELS[
                interest as keyof typeof SERVICE_INTEREST_LABELS
              ]
          )
          .join(", ")}
      </td>
      <td>{BUDGET_RANGE_LABELS[signup.budgetRange]}</td>
      <td>
        {signup.status == "pending" ? (
          <span className="badge bg-warning-transparent">Pending</span>
        ) : signup.status == "approved" ? (
          <span className="badge bg-success-transparent">Approved</span>
        ) : signup.status == "rejected" ? (
          <span className="badge bg-danger-transparent">Rejected</span>
        ) : null}
      </td>
      <td>
        {signup.createdAt
          ? format(new Date(signup.createdAt), "yyyy/M/d h:mm aa")
          : ""}
      </td>
      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>View</Tooltip>}>
          <Button
            hidden={
              !hasPermission(ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)
            }
            variant="info-light"
            className="btn btn-info-light btn-sm ms-2"
            onClick={() => navigate(`details/${signup.id}`)}
          >
            <span className="ri-eye-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
          <Button
            hidden={
              !hasPermission(ACTION.UPDATE, RESOURCE.SALON_CONSTRUCTION_SERVICE)
            }
            variant="warning-light"
            className="btn btn-warning-light btn-sm ms-2"
            onClick={() => navigate(`details/${signup.id}/edit`)}
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            hidden={
              !hasPermission(ACTION.DELETE, RESOURCE.SALON_CONSTRUCTION_SERVICE)
            }
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            onClick={() => handleDeleteClick(signup.id, signup.businessName)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td>
    </tr>
  );
};

export default ManagementSalonConstructionService;
